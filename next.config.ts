import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  compress: true, // Enable compression
  images: {
    formats: ['image/avif', 'image/webp'], // Modern image formats
    minimumCacheTTL: 86400, // 1 day cache for images
    domains: ['res.cloudinary.com'],
  },
  experimental: {
    optimizePackageImports: ['@mui/material'], // Optional
  },
  allowedDevOrigins: ['https://0311-102-0-0-242.ngrok-free.app'],

};

export default nextConfig;
