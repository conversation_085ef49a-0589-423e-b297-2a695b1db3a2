'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/lib/context/ThemeContext';
import { useRouter } from 'next/navigation';
import { GlassmorphicCard } from './GlassmorphicCard';
import { CrefyButton } from './CrefyButton';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHome, faUser, faCog, faSignOutAlt } from '@fortawesome/free-solid-svg-icons';

interface NavItem {
    label: string;
    href: string;
    icon?: any;
}

interface CrefyNavbarProps {
    title?: string;
    navItems?: NavItem[];
    showAuth?: boolean;
    onLogout?: () => void;
    className?: string;
    userAddress?: string;
}

export const CrefyNavbar: React.FC<CrefyNavbarProps> = ({
    title = 'Crefy',
    navItems = [],
    showAuth = false,
    onLogout,
    className = '',
    userAddress
}) => {
    const { theme } = useTheme();
    const router = useRouter();
    const [currentUserAddress, setCurrentUserAddress] = useState<string>('');

    // Remove Dashboard and Home links from default navigation
    const defaultNavItems: NavItem[] = [];

    const items = navItems.length > 0 ? navItems : defaultNavItems;

    useEffect(() => {
        // Get user address from props or localStorage
        const address = userAddress || localStorage.getItem('walletAddress') || '';
        setCurrentUserAddress(address);
    }, [userAddress]);

    const handleNavigation = (href: string) => {
        router.push(href);
    };

    const formatAddress = (address: string) => {
        if (!address) return '';
        return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };

    return (
        <motion.nav
            className={`fixed top-0 left-0 right-0 z-50 p-2 ${className}`}
            initial={{ y: -100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
        >
            <div className="max-w-7xl mx-auto">
                <GlassmorphicCard
                    padding="xs"
                    rounded="xl"
                    className="flex items-center justify-between h-14"
                    style={{
                        backgroundColor: theme.colors.glass,
                        backdropFilter: 'blur(20px)',
                        border: `1px solid ${theme.colors.glassBorder}`,
                    }}
                >
                    {/* Logo/Title */}
                    <motion.div
                        className="flex items-center space-x-3 cursor-pointer"
                        onClick={() => handleNavigation('/')}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                    >
                        <div
                            className="w-8 h-8 rounded-lg flex items-center justify-center"
                            style={{ backgroundColor: theme.colors.primary }}
                        >
                            <span className="text-white font-bold text-sm">C</span>
                        </div>
                        <h1
                            className="text-lg font-bold"
                            style={{ color: theme.colors.text }}
                        >
                            {title}
                        </h1>
                    </motion.div>

                    {/* Navigation Items */}
                    <div className="hidden md:flex items-center space-x-2">
                        {items.map((item, index) => (
                            <motion.button
                                key={index}
                                className="flex items-center space-x-2 px-3 py-1.5 rounded-lg transition-all duration-200"
                                style={{
                                    color: theme.colors.text,
                                    backgroundColor: 'transparent',
                                }}
                                onClick={() => handleNavigation(item.href)}
                                whileHover={{
                                    backgroundColor: theme.colors.glass,
                                    scale: 1.05
                                }}
                                whileTap={{ scale: 0.95 }}
                            >
                                {item.icon && (
                                    <FontAwesomeIcon
                                        icon={item.icon}
                                        className="w-4 h-4"
                                    />
                                )}
                                <span className="font-medium">{item.label}</span>
                            </motion.button>
                        ))}
                    </div>

                    {/* Auth Section */}
                    <div className="flex items-center space-x-3">
                        {showAuth && currentUserAddress ? (
                            <>
                                {/* User Wallet Info */}
                                <GlassmorphicCard
                                    padding="xs"
                                    className="flex items-center space-x-2"
                                >
                                    <div
                                        className="w-6 h-6 rounded-md flex items-center justify-center"
                                        style={{ backgroundColor: theme.colors.primary }}
                                    >
                                        <FontAwesomeIcon
                                            icon={faUser}
                                            className="text-white text-xs"
                                        />
                                    </div>
                                    <div className="text-left">
                                        <p
                                            className="text-xs font-medium"
                                            style={{ color: theme.colors.text }}
                                        >
                                            {formatAddress(currentUserAddress)}
                                        </p>
                                    </div>
                                    <div
                                        className="w-1.5 h-1.5 rounded-full"
                                        style={{ backgroundColor: '#4CAF50' }}
                                        title="Verified"
                                    />
                                </GlassmorphicCard>
                                {onLogout && (
                                    <CrefyButton
                                        variant="secondary"
                                        size="sm"
                                        onClick={onLogout}
                                    >
                                        <FontAwesomeIcon icon={faSignOutAlt} className="mr-2" />
                                        Logout
                                    </CrefyButton>
                                )}
                            </>
                        ) : showAuth ? (
                            <CrefyButton
                                variant="primary"
                                size="sm"
                                onClick={() => handleNavigation('/login')}
                            >
                                Sign In
                            </CrefyButton>
                        ) : (
                            <CrefyButton
                                variant="primary"
                                size="sm"
                                onClick={() => handleNavigation('/login')}
                            >
                                Sign In
                            </CrefyButton>
                        )}
                    </div>
                </GlassmorphicCard>
            </div>
        </motion.nav>
    );
};
