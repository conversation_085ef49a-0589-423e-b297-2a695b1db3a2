'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/lib/context/ThemeContext';

interface CrefyButtonProps {
    children: React.ReactNode;
    onClick?: () => void;
    variant?: 'primary' | 'secondary' | 'accent' | 'glass';
    size?: 'sm' | 'md' | 'lg' | 'xl';
    disabled?: boolean;
    loading?: boolean;
    className?: string;
    type?: 'button' | 'submit' | 'reset';
    fullWidth?: boolean;
}

export const CrefyButton: React.FC<CrefyButtonProps> = ({
    children,
    onClick,
    variant = 'primary',
    size = 'md',
    disabled = false,
    loading = false,
    className = '',
    type = 'button',
    fullWidth = false
}) => {
    const { theme } = useTheme();

    const sizeClasses = {
        sm: 'px-4 py-2 text-sm',
        md: 'px-6 py-3 text-base',
        lg: 'px-8 py-4 text-lg',
        xl: 'px-10 py-5 text-xl'
    };

    const getVariantStyles = () => {
        switch (variant) {
            case 'primary':
                return {
                    backgroundColor: theme.colors.primary,
                    color: '#FFFFFF',
                    border: `1px solid ${theme.colors.primary}`,
                };
            case 'secondary':
                return {
                    backgroundColor: theme.colors.secondary,
                    color: '#FFFFFF',
                    border: `1px solid ${theme.colors.secondary}`,
                };
            case 'accent':
                return {
                    backgroundColor: theme.colors.accent,
                    color: '#FFFFFF',
                    border: `1px solid ${theme.colors.accent}`,
                };
            case 'glass':
                return {
                    backgroundColor: theme.colors.glass,
                    color: theme.colors.text,
                    border: `1px solid ${theme.colors.glassBorder}`,
                    backdropFilter: 'blur(10px)',
                };
            default:
                return {
                    backgroundColor: theme.colors.primary,
                    color: '#FFFFFF',
                    border: `1px solid ${theme.colors.primary}`,
                };
        }
    };

    const baseClasses = `
        ${sizeClasses[size]}
        ${fullWidth ? 'w-full' : ''}
        rounded-xl
        font-semibold
        transition-all
        duration-300
        transform
        hover:scale-105
        hover:shadow-lg
        active:scale-95
        focus:outline-none
        focus:ring-4
        focus:ring-opacity-50
        disabled:opacity-50
        disabled:cursor-not-allowed
        disabled:transform-none
        ${className}
    `.trim().replace(/\s+/g, ' ');

    const buttonStyle = {
        ...getVariantStyles(),
        boxShadow: disabled ? 'none' : '0 4px 15px rgba(0, 0, 0, 0.1)',
        focusRingColor: theme.colors.primary + '50',
    };

    return (
        <motion.button
            type={type}
            className={baseClasses}
            style={buttonStyle}
            onClick={onClick}
            disabled={disabled || loading}
            whileHover={!disabled && !loading ? { scale: 1.05 } : {}}
            whileTap={!disabled && !loading ? { scale: 0.95 } : {}}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
        >
            {loading ? (
                <div className="flex items-center justify-center">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Loading...
                </div>
            ) : (
                children
            )}
        </motion.button>
    );
};
