'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/lib/context/ThemeContext';

interface GlassmorphicCardProps {
    children: React.ReactNode;
    className?: string;
    padding?: 'sm' | 'md' | 'lg' | 'xl';
    blur?: 'sm' | 'md' | 'lg';
    rounded?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
    border?: boolean;
    shadow?: boolean;
    hover?: boolean;
    onClick?: () => void;
    style?: React.CSSProperties;
}

export const GlassmorphicCard: React.FC<GlassmorphicCardProps> = ({
    children,
    className = '',
    padding = 'md',
    blur = 'md',
    rounded = 'xl',
    border = true,
    shadow = true,
    hover = false,
    onClick,
    style = {}
}) => {
    const { theme } = useTheme();

    const paddingClasses = {
        sm: 'p-3',
        md: 'p-6',
        lg: 'p-8',
        xl: 'p-12'
    };

    const blurClasses = {
        sm: 'backdrop-blur-sm',
        md: 'backdrop-blur-md',
        lg: 'backdrop-blur-lg'
    };

    const roundedClasses = {
        sm: 'rounded-sm',
        md: 'rounded-md',
        lg: 'rounded-lg',
        xl: 'rounded-xl',
        '2xl': 'rounded-2xl',
        '3xl': 'rounded-3xl'
    };

    const baseClasses = `
        ${paddingClasses[padding]}
        ${blurClasses[blur]}
        ${roundedClasses[rounded]}
        ${border ? 'border' : ''}
        ${shadow ? 'shadow-lg' : ''}
        ${hover ? 'transition-all duration-300 hover:scale-105 hover:shadow-xl' : ''}
        ${onClick ? 'cursor-pointer' : ''}
        ${className}
    `.trim().replace(/\s+/g, ' ');

    const cardStyle = {
        backgroundColor: theme.colors.glass,
        borderColor: border ? theme.colors.glassBorder : 'transparent',
        ...style
    };

    if (onClick) {
        return (
            <motion.div
                className={baseClasses}
                style={cardStyle}
                onClick={onClick}
                whileHover={hover ? { scale: 1.02 } : {}}
                whileTap={hover ? { scale: 0.98 } : {}}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
            >
                {children}
            </motion.div>
        );
    }

    return (
        <motion.div
            className={baseClasses}
            style={cardStyle}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
        >
            {children}
        </motion.div>
    );
};
