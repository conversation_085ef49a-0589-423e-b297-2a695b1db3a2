'use client';

import React, { useRef, useEffect } from 'react';
import { motion, useScroll, useTransform, useInView } from 'framer-motion';

interface AnimatedScrollContainerProps {
    children: React.ReactNode;
    className?: string;
    animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale' | 'parallax';
    delay?: number;
    duration?: number;
    offset?: number;
    once?: boolean;
}

export const AnimatedScrollContainer: React.FC<AnimatedScrollContainerProps> = ({
    children,
    className = '',
    animation = 'fadeIn',
    delay = 0,
    duration = 0.6,
    offset = 0.1,
    once = true
}) => {
    const ref = useRef<HTMLDivElement>(null);
    const isInView = useInView(ref, { 
        once, 
        margin: `-${offset * 100}% 0px -${offset * 100}% 0px` 
    });
    const { scrollYProgress } = useScroll({
        target: ref,
        offset: ["start end", "end start"]
    });

    // Parallax transforms
    const y = useTransform(scrollYProgress, [0, 1], [100, -100]);
    const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

    const getAnimationVariants = () => {
        switch (animation) {
            case 'fadeIn':
                return {
                    hidden: { opacity: 0 },
                    visible: { 
                        opacity: 1,
                        transition: { duration, delay }
                    }
                };
            
            case 'slideUp':
                return {
                    hidden: { opacity: 0, y: 50 },
                    visible: { 
                        opacity: 1, 
                        y: 0,
                        transition: { duration, delay, ease: "easeOut" }
                    }
                };
            
            case 'slideLeft':
                return {
                    hidden: { opacity: 0, x: 50 },
                    visible: { 
                        opacity: 1, 
                        x: 0,
                        transition: { duration, delay, ease: "easeOut" }
                    }
                };
            
            case 'slideRight':
                return {
                    hidden: { opacity: 0, x: -50 },
                    visible: { 
                        opacity: 1, 
                        x: 0,
                        transition: { duration, delay, ease: "easeOut" }
                    }
                };
            
            case 'scale':
                return {
                    hidden: { opacity: 0, scale: 0.8 },
                    visible: { 
                        opacity: 1, 
                        scale: 1,
                        transition: { duration, delay, ease: "easeOut" }
                    }
                };
            
            case 'parallax':
                return {
                    hidden: { opacity: 0 },
                    visible: { 
                        opacity: 1,
                        transition: { duration, delay }
                    }
                };
            
            default:
                return {
                    hidden: { opacity: 0 },
                    visible: { 
                        opacity: 1,
                        transition: { duration, delay }
                    }
                };
        }
    };

    const variants = getAnimationVariants();

    if (animation === 'parallax') {
        return (
            <motion.div
                ref={ref}
                className={className}
                style={{ y, opacity }}
            >
                {children}
            </motion.div>
        );
    }

    return (
        <motion.div
            ref={ref}
            className={className}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={variants}
        >
            {children}
        </motion.div>
    );
};

// Staggered children animation container
interface StaggeredContainerProps {
    children: React.ReactNode;
    className?: string;
    staggerDelay?: number;
    childAnimation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale';
    once?: boolean;
}

export const StaggeredContainer: React.FC<StaggeredContainerProps> = ({
    children,
    className = '',
    staggerDelay = 0.1,
    childAnimation = 'slideUp',
    once = true
}) => {
    const ref = useRef<HTMLDivElement>(null);
    const isInView = useInView(ref, { once });

    const containerVariants = {
        hidden: {},
        visible: {
            transition: {
                staggerChildren: staggerDelay
            }
        }
    };

    const getChildVariants = () => {
        switch (childAnimation) {
            case 'fadeIn':
                return {
                    hidden: { opacity: 0 },
                    visible: { opacity: 1, transition: { duration: 0.6 } }
                };
            case 'slideUp':
                return {
                    hidden: { opacity: 0, y: 30 },
                    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
                };
            case 'slideLeft':
                return {
                    hidden: { opacity: 0, x: 30 },
                    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } }
                };
            case 'slideRight':
                return {
                    hidden: { opacity: 0, x: -30 },
                    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } }
                };
            case 'scale':
                return {
                    hidden: { opacity: 0, scale: 0.8 },
                    visible: { opacity: 1, scale: 1, transition: { duration: 0.6 } }
                };
            default:
                return {
                    hidden: { opacity: 0, y: 30 },
                    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
                };
        }
    };

    const childVariants = getChildVariants();

    return (
        <motion.div
            ref={ref}
            className={className}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
        >
            {React.Children.map(children, (child, index) => (
                <motion.div key={index} variants={childVariants}>
                    {child}
                </motion.div>
            ))}
        </motion.div>
    );
};
