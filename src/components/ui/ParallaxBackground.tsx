'use client';

import React, { useEffect, useRef } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useTheme } from '@/lib/context/ThemeContext';

interface FloatingOrb {
    id: number;
    x: number;
    y: number;
    size: number;
    color: string;
    opacity: number;
    duration: number;
    delay: number;
}

interface ParallaxBackgroundProps {
    children?: React.ReactNode;
    orbCount?: number;
    className?: string;
}

export const ParallaxBackground: React.FC<ParallaxBackgroundProps> = ({
    children,
    orbCount = 12,
    className = ''
}) => {
    const { theme } = useTheme();
    const containerRef = useRef<HTMLDivElement>(null);
    const orbsRef = useRef<FloatingOrb[]>([]);

    // Generate floating orbs
    useEffect(() => {
        const colors = [
            theme.colors.primary,
            theme.colors.accent,
            theme.colors.accentLight,
            theme.colors.secondary,
        ];

        orbsRef.current = Array.from({ length: orbCount }, (_, i) => ({
            id: i,
            x: Math.random() * 100,
            y: Math.random() * 100,
            size: Math.random() * 250 + 120, // 120-370px
            color: colors[Math.floor(Math.random() * colors.length)],
            opacity: Math.random() * 0.4 + 0.15, // 0.15-0.55
            duration: Math.random() * 25 + 20, // 20-45 seconds
            delay: Math.random() * 8, // 0-8 seconds delay
        }));
    }, [theme, orbCount]);

    const FloatingOrb: React.FC<{ orb: FloatingOrb }> = ({ orb }) => {
        const controls = useAnimation();

        useEffect(() => {
            const animateOrb = async () => {
                await controls.start({
                    x: [0, Math.random() * 100 - 50, 0],
                    y: [0, Math.random() * 100 - 50, 0],
                    scale: [1, Math.random() * 0.5 + 0.8, 1],
                    transition: {
                        duration: orb.duration,
                        ease: "easeInOut",
                        repeat: Infinity,
                        delay: orb.delay,
                    }
                });
            };

            animateOrb();
        }, [controls, orb]);

        return (
            <motion.div
                className="absolute rounded-full blur-3xl"
                style={{
                    left: `${orb.x}%`,
                    top: `${orb.y}%`,
                    width: `${orb.size}px`,
                    height: `${orb.size}px`,
                    background: `radial-gradient(circle, ${orb.color}40 0%, ${orb.color}20 50%, transparent 100%)`,
                    opacity: orb.opacity,
                    filter: 'blur(80px)',
                }}
                animate={controls}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: orb.opacity }}
                transition={{ duration: 2 }}
            />
        );
    };

    return (
        <div 
            ref={containerRef}
            className={`relative min-h-screen overflow-hidden ${className}`}
            style={{
                background: `linear-gradient(135deg, 
                    ${theme.colors.background} 0%, 
                    ${theme.colors.background}f0 25%, 
                    ${theme.colors.background}e0 50%, 
                    ${theme.colors.background}f0 75%, 
                    ${theme.colors.background} 100%)`
            }}
        >
            {/* Floating Orbs */}
            <div className="absolute inset-0 overflow-hidden">
                {orbsRef.current.map((orb) => (
                    <FloatingOrb key={orb.id} orb={orb} />
                ))}
            </div>

            {/* Gradient Overlay */}
            <div 
                className="absolute inset-0 pointer-events-none"
                style={{
                    background: `radial-gradient(circle at 50% 50%, 
                        transparent 0%, 
                        ${theme.colors.background}20 70%, 
                        ${theme.colors.background}40 100%)`
                }}
            />

            {/* Content */}
            <div className="relative z-10">
                {children}
            </div>
        </div>
    );
};
