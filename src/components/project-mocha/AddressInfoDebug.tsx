// components/project-mocha/AddressInfoDebug.tsx
"use client"

import { useAddressInfo } from '@/hooks/use-address-info';
import { useActiveAccount } from 'thirdweb/react';
import { useContract } from '@/hooks/useContract';

export const AddressInfoDebug = () => {
    const account = useActiveAccount();
    const { account: contractAccount } = useContract();
    const userAddress = contractAccount || account?.address;
    const { data: addressInfo, isLoading, error } = useAddressInfo(userAddress);

    return (
        <div className="bg-gray-800 p-4 rounded-lg mt-4 text-xs">
            <h3 className="text-white font-bold mb-2">Address Info Debug</h3>
            
            <div className="space-y-2">
                <div>
                    <span className="text-gray-400">Account Address: </span>
                    <span className="text-white">{account?.address || 'Not connected'}</span>
                </div>
                
                <div>
                    <span className="text-gray-400">Contract Account: </span>
                    <span className="text-white">{contractAccount || 'Undefined'}</span>
                </div>
                
                <div>
                    <span className="text-gray-400">User Address: </span>
                    <span className="text-white">{userAddress || 'Undefined'}</span>
                </div>
                
                <div>
                    <span className="text-gray-400">Is Loading: </span>
                    <span className="text-white">{isLoading ? 'Yes' : 'No'}</span>
                </div>
                
                <div>
                    <span className="text-gray-400">Error: </span>
                    <span className="text-red-400">{error?.message || 'None'}</span>
                </div>
                
                <div>
                    <span className="text-gray-400">Address Info: </span>
                    <div className="text-white ml-4">
                        {addressInfo ? (
                            <div>
                                <div>Current Balance: {addressInfo.currentBalance}</div>
                                <div>Total Minted: {addressInfo.totalMinted}</div>
                                <div>Remaining Mints: {addressInfo.remainingMints}</div>
                            </div>
                        ) : (
                            <span className="text-red-400">Undefined</span>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};
