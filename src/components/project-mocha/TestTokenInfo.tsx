// components/project-mocha/TestTokenInfo.tsx
"use client"

import { useTokenInfo, useMintingStats } from '@/hooks/useTokenInfo';
import { useActiveAccount } from 'thirdweb/react';
import { redemptionService } from '@/lib/services/redemptionService';
import { useState } from 'react';

interface TestTokenInfoProps {
    tokenId: string | null;
}

export const TestTokenInfo = ({ tokenId }: TestTokenInfoProps) => {
    const account = useActiveAccount();
    const { tokenInfo } = useTokenInfo(tokenId);
    const { mintingStats, userBalance } = useMintingStats(account?.address);
    const [testResult, setTestResult] = useState<string>('');

    const runTest = async () => {
        if (!tokenId || !account?.address) {
            setTestResult('Missing tokenId or account address');
            return;
        }

        try {
            // Test redemption service
            const isRedeemed = redemptionService.isTokenRedeemed(tokenId, account.address);
            const userStats = redemptionService.getUserRedemptionStats(account.address, [tokenId]);
            
            const results = {
                tokenId,
                tokenInfo,
                mintingStats,
                userBalance,
                redemptionStats: {
                    isRedeemed,
                    userStats
                }
            };

            setTestResult(JSON.stringify(results, null, 2));
            console.log('Token Info Test Results:', results);
        } catch (error) {
            setTestResult(`Error: ${error}`);
            console.error('Test error:', error);
        }
    };

    const simulateRedemption = async () => {
        if (!tokenId || !account?.address) {
            setTestResult('Missing tokenId or account address');
            return;
        }

        try {
            await redemptionService.simulateRedemption(tokenId, account.address);
            setTestResult('Redemption simulated successfully!');
            // Re-run test to see updated stats
            setTimeout(runTest, 500);
        } catch (error) {
            setTestResult(`Redemption error: ${error}`);
        }
    };

    const clearRedemptions = () => {
        redemptionService.clearAllRedemptions();
        setTestResult('All redemptions cleared');
        setTimeout(runTest, 500);
    };

    if (!tokenId) {
        return (
            <div className="bg-gray-800 p-4 rounded-lg mt-4">
                <h3 className="text-white font-bold mb-2">Token Info Test</h3>
                <p className="text-gray-400">No token ID available for testing</p>
            </div>
        );
    }

    return (
        <div className="bg-gray-800 p-4 rounded-lg mt-4">
            <h3 className="text-white font-bold mb-2">Token Info Test</h3>
            <p className="text-gray-400 mb-2">Token ID: {tokenId}</p>
            
            <div className="flex gap-2 mb-4">
                <button
                    onClick={runTest}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                >
                    Run Test
                </button>
                <button
                    onClick={simulateRedemption}
                    className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                >
                    Simulate Redemption
                </button>
                <button
                    onClick={clearRedemptions}
                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                >
                    Clear Redemptions
                </button>
            </div>

            {testResult && (
                <div className="bg-gray-900 p-3 rounded text-xs text-green-400 overflow-auto max-h-40">
                    <pre>{testResult}</pre>
                </div>
            )}
        </div>
    );
};
