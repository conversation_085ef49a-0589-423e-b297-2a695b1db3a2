export type Theme = {
    colors: {
        primary: string;
        primaryLight: string;
        secondary: string;
        success: string;
        successLight: string;
        background: string;
        card: string;
        cardBackground: string;
        text: string;
        secondaryText: string;
        border: string;
        warning: string;
        accent: string;
        accentLight: string;
        glass: string;
        glassBorder: string;
    };
};

export interface TokenData {
    id: string;
    name?: string;
    type: string;
    issuer: string;
    timestamp: number;
    mintedDate: string;
    mintedTime: string;
    validUntil: string;
    validUntilFormatted: string;
    redemptions: number;
    maxRedemptions: number;
    totalUserRedemptions?: number; // Total redemptions available to user across all tokens
    usedUserRedemptions?: number;  // Total redemptions used by user across all tokens
    value: string;
    rarity: string;
}