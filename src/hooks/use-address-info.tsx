/**
 * Custom hook for fetching detailed address information
 * Uses the getAddressInfo function from the smart contract
 * Automatically refetches data every 2 minutes (120 seconds)
 */
"use client"

import { useReadContract } from "thirdweb/react"
import { getContract } from "thirdweb"
import { CONTRACT_CONFIG } from "@/config/contract"
import { sepolia } from "thirdweb/chains"
import { client } from '@/config/client'

// Initialize the contract once
const contract = getContract({
    address: CONTRACT_CONFIG.address,
    chain: sepolia,
    client,
});


interface AddressInfo {
    currentBalance: number
    totalMinted: number
    remainingMints: number
}

export function useAddressInfo(address: string | undefined) {
    const { data, isLoading, error, refetch } = useReadContract({
        contract,
        method: "function getAddressInfo(address) view returns (uint256, uint256, uint256)",
        params: address ? [address] : undefined,
        query: {
            enabled: !!address,
            refetchInterval: 120000, // Refetch every 2 minutes (120 seconds)
            staleTime: 60000, // Consider data stale after 1 minute
        },
    })

    const addressInfo: AddressInfo | undefined = data
        ? {
            currentBalance: Number(data[0]),
            totalMinted: Number(data[1]),
            remainingMints: Number(data[2]),
        }
        : undefined

    console.log("Address info debug:", {
        address,
        data,
        addressInfo,
        isLoading,
        error: error?.message
    });

    return {
        data: addressInfo,
        isLoading,
        error,
        refetch,
    }
}