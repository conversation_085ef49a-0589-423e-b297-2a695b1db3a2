// hooks/useTokenInfo.ts
"use client"

import { useReadContract } from "thirdweb/react";
import { getContract } from "thirdweb";
import { CONTRACT_CONFIG } from "@/config/contract";
import { sepolia } from "thirdweb/chains";
import { client } from '@/config/client';

// Initialize the contract once
const contract = getContract({
    address: CONTRACT_CONFIG.address,
    chain: sepolia,
    client,
});

interface TokenInfo {
    tokenId: string;
    owner: string;
    tokenURI: string;
    exists: boolean;
}

interface MintingStats {
    totalSupply: number;
    userMintCount: number;
    globalMintCount: number;
}

export function useTokenInfo(tokenId: string | null) {
    // Get token owner
    const { data: owner } = useReadContract({
        contract,
        method: "function ownerOf(uint256) view returns (address)",
        params: tokenId ? [BigInt(tokenId)] : undefined,
        query: {
            enabled: !!tokenId,
        },
    });

    // Get token URI
    const { data: tokenURI } = useReadContract({
        contract,
        method: "function tokenURI(uint256) view returns (string)",
        params: tokenId ? [BigInt(tokenId)] : undefined,
        query: {
            enabled: !!tokenId,
        },
    });

    const tokenInfo: TokenInfo | null = tokenId ? {
        tokenId,
        owner: owner || "",
        tokenURI: tokenURI || "",
        exists: !!owner,
    } : null;

    return {
        tokenInfo,
        isLoading: !tokenId || (!owner && !tokenURI),
    };
}

export function useMintingStats(userAddress: string | undefined) {
    // Get total supply
    const { data: totalSupply } = useReadContract({
        contract,
        method: "function totalSupply() view returns (uint256)",
        query: {
            refetchInterval: 30000, // Refetch every 30 seconds
        },
    });

    // Get user's mint count
    const { data: userMintCount } = useReadContract({
        contract,
        method: "function getMintCount(address) view returns (uint256)",
        params: userAddress ? [userAddress] : undefined,
        query: {
            enabled: !!userAddress,
            refetchInterval: 30000,
        },
    });

    // Get user's current balance
    const { data: userBalance } = useReadContract({
        contract,
        method: "function balanceOf(address) view returns (uint256)",
        params: userAddress ? [userAddress] : undefined,
        query: {
            enabled: !!userAddress,
            refetchInterval: 30000,
        },
    });

    const mintingStats: MintingStats = {
        totalSupply: totalSupply ? Number(totalSupply) : 0,
        userMintCount: userMintCount ? Number(userMintCount) : 0,
        globalMintCount: totalSupply ? Number(totalSupply) : 0,
    };

    return {
        mintingStats,
        userBalance: userBalance ? Number(userBalance) : 0,
        isLoading: !totalSupply,
    };
}

// Hook to get comprehensive token statistics for display
export function useTokenStats(tokenId: string | null, userAddress: string | undefined) {
    const { tokenInfo } = useTokenInfo(tokenId);
    const { mintingStats, userBalance } = useMintingStats(userAddress);

    // Calculate redemption statistics
    // Note: This would ideally come from a backend service that tracks redemptions
    // For now, we'll use the token existence and ownership as a proxy
    const redemptionStats = {
        tokenRedemptions: 0, // This token's redemptions (would come from backend)
        maxTokenRedemptions: 1, // Each token allows 1 redemption
        totalUserRedemptions: userBalance, // Total redemptions available to user
        usedUserRedemptions: 0, // Would come from backend tracking
    };

    return {
        tokenInfo,
        mintingStats,
        redemptionStats,
        userBalance,
    };
}
