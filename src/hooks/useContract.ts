// hooks/useContract.ts
"use client"
import { useState } from "react";
import {
    useSendTransaction,
    useActiveAccount,
    useReadContract,
    useConnectModal
} from "thirdweb/react";
import {
    prepareContractCall,
    getContract,
    getContractEvents,
} from "thirdweb";
import { CONTRACT_CONFIG } from "@/config/contract";
import { sepolia } from "thirdweb/chains";
import { client } from '@/config/client';

// Initialize the contract once
const contract = getContract({
    address: CONTRACT_CONFIG.address,
    chain: sepolia,
    client,
});

type MintResult = {
    receipt: any;
    tokenId: string | null;
};

type UseContractReturn = {
    account: string | undefined;
    status: string;
    connectWallet: () => Promise<void>;
    mintToken: () => Promise<MintResult>;
    isConnected: boolean;
    tokenMinted?: bigint;
};

export function useContract(): UseContractReturn {
    const [status, setStatus] = useState<string>("");
    const { connect } = useConnectModal();
    const account: any = useActiveAccount();
    const address = account?.address;
    const isConnected = !!account;
    const { mutateAsync: sendTx } = useSendTransaction();

    // Example read method - adjust based on your contract
    const { data: tokenMinted } = useReadContract({
        contract,
        method: "function balanceOf(address) view returns (uint256)",
        params: [address || "0x"],
    });

    const connectWallet = async () => {
        try {
            await connect({ client });
            setStatus("Wallet connected!");
        } catch (error) {
            console.error(error);
            setStatus("Failed to connect wallet.");
        }
    };


    const mintToken = async (): Promise<MintResult> => {
        if (!account) {
            setStatus("Wallet not connected");
            throw new Error("Wallet not connected");
        }

        try {
            setStatus("Preparing transaction...");

            // Using prepareContractCall for custom methods
            const transaction = prepareContractCall({
                contract,
                method: "function mintFromContract()",
            });

            setStatus("Sending transaction...");
            const receipt = await sendTx(transaction);
            setStatus("Token minted successfully!");

            // Extract tokenId from transaction receipt
            let tokenId: string | null = null;

            try {
                // Get Transfer events from the transaction to extract tokenId
                const events = await getContractEvents({
                    contract,
                    fromBlock: receipt.blockNumber,
                    toBlock: receipt.blockNumber,
                    eventName: "Transfer",
                });

                // Find the Transfer event for this transaction
                const transferEvent = events.find((event: any) =>
                    event.transactionHash === receipt.transactionHash &&
                    event.args?.to?.toLowerCase() === account.address.toLowerCase()
                );

                if (transferEvent && transferEvent.args?.tokenId) {
                    tokenId = transferEvent.args.tokenId.toString();
                    console.log("Extracted tokenId from blockchain:", tokenId);
                }
            } catch (eventError) {
                console.error("Error extracting tokenId from events:", eventError);
                // Fallback: try to get tokenId from transaction logs
                if (receipt.logs && receipt.logs.length > 0) {
                    // Look for Transfer event in logs
                    const transferLog = receipt.logs.find((log: any) =>
                        log.topics && log.topics[0] === "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"
                    );
                    if (transferLog && transferLog.topics && transferLog.topics.length >= 4) {
                        // tokenId is typically the 4th topic in Transfer events
                        tokenId = parseInt(transferLog.topics[3], 16).toString();
                        console.log("Extracted tokenId from logs:", tokenId);
                    }
                }
            }

            return { receipt, tokenId };
        } catch (error) {
            console.error(error);
            setStatus("Minting failed.");
            throw error;
        }
    };

    return {
        account: address, // Return the address string instead of the full account object
        status,
        connectWallet,
        mintToken,
        isConnected,
        tokenMinted,
    };
}