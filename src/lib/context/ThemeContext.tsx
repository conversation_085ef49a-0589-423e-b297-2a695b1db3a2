import React, { createContext, useContext, useState } from 'react';
import { Theme } from '@/types/types';

const lightTheme: Theme = {
    colors: {
        primary: '#4B2C6F', // Deep purple
        primaryLight: 'rgba(75, 44, 111, 0.1)',
        secondary: '#8161A9', // Muted violet
        success: '#10B981',
        successLight: 'rgba(16, 185, 129, 0.1)',
        background: '#FFFFFF', // Base white
        card: '#FFFFFF',
        cardBackground: '#FFFFFF',
        text: '#1F2937',
        secondaryText: '#6B7280',
        border: '#E5E7EB',
        warning: '#F59E0B',
        accent: '#B89CD9', // Lavender
        accentLight: '#A78BCA', // Light purple
        glass: 'rgba(255, 255, 255, 0.1)',
        glassBorder: 'rgba(255, 255, 255, 0.2)',
    },
};

const darkTheme: Theme = {
    colors: {
        primary: '#A78BCA', // Light purple for dark mode
        primaryLight: 'rgba(167, 139, 202, 0.1)',
        secondary: '#B89CD9', // Lavender for dark mode
        success: '#10B981',
        successLight: 'rgba(16, 185, 129, 0.2)',
        background: '#0A0A0A', // Darker background
        card: '#1A1A1A',
        cardBackground: '#1A1A1A',
        text: '#FFFFFF',
        secondaryText: '#D1D5DB',
        border: '#374151',
        warning: '#F59E0B',
        accent: '#8161A9', // Muted violet for dark mode
        accentLight: '#4B2C6F', // Deep purple for dark mode
        glass: 'rgba(0, 0, 0, 0.2)',
        glassBorder: 'rgba(255, 255, 255, 0.1)',
    },
};

const ThemeContext = createContext({
    theme: lightTheme,
    toggleTheme: () => { },
    isDark: false,
});

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
    const [isDark, setIsDark] = useState(false);

    const toggleTheme = () => setIsDark(!isDark);

    const theme = isDark ? darkTheme : lightTheme;

    return (
        <ThemeContext.Provider value={{ theme, toggleTheme, isDark }}>
            {children}
        </ThemeContext.Provider>
    );
};

export const useTheme = () => useContext(ThemeContext); 