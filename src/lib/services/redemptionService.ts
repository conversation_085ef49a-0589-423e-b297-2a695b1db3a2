// lib/services/redemptionService.ts
"use client"

interface RedemptionRecord {
    tokenId: string;
    userAddress: string;
    timestamp: number;
    location?: string;
    transactionHash?: string;
}

interface TokenRedemptionStats {
    tokenId: string;
    totalRedemptions: number;
    maxRedemptions: number;
    lastRedemptionDate?: Date;
}

interface UserRedemptionStats {
    userAddress: string;
    totalRedemptions: number;
    availableRedemptions: number;
    tokens: TokenRedemptionStats[];
}

class RedemptionService {
    private readonly STORAGE_KEY = 'coffee_token_redemptions';
    private readonly USER_STATS_KEY = 'user_redemption_stats';

    // Get redemption records from localStorage (placeholder for backend)
    private getRedemptionRecords(): RedemptionRecord[] {
        if (typeof window === 'undefined') return [];
        
        try {
            const stored = localStorage.getItem(this.STORAGE_KEY);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Error reading redemption records:', error);
            return [];
        }
    }

    // Save redemption records to localStorage (placeholder for backend)
    private saveRedemptionRecords(records: RedemptionRecord[]): void {
        if (typeof window === 'undefined') return;
        
        try {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(records));
        } catch (error) {
            console.error('Error saving redemption records:', error);
        }
    }

    // Record a token redemption
    async recordRedemption(
        tokenId: string, 
        userAddress: string, 
        location?: string,
        transactionHash?: string
    ): Promise<boolean> {
        try {
            const records = this.getRedemptionRecords();
            
            // Check if token has already been redeemed
            const existingRedemption = records.find(
                record => record.tokenId === tokenId && record.userAddress.toLowerCase() === userAddress.toLowerCase()
            );
            
            if (existingRedemption) {
                throw new Error('Token has already been redeemed');
            }

            // Add new redemption record
            const newRecord: RedemptionRecord = {
                tokenId,
                userAddress: userAddress.toLowerCase(),
                timestamp: Date.now(),
                location,
                transactionHash
            };

            records.push(newRecord);
            this.saveRedemptionRecords(records);

            console.log('Redemption recorded:', newRecord);
            return true;
        } catch (error) {
            console.error('Error recording redemption:', error);
            throw error;
        }
    }

    // Get redemption stats for a specific token
    getTokenRedemptionStats(tokenId: string): TokenRedemptionStats {
        const records = this.getRedemptionRecords();
        const tokenRedemptions = records.filter(record => record.tokenId === tokenId);
        
        const lastRedemption = tokenRedemptions.length > 0 
            ? new Date(Math.max(...tokenRedemptions.map(r => r.timestamp)))
            : undefined;

        return {
            tokenId,
            totalRedemptions: tokenRedemptions.length,
            maxRedemptions: 1, // Each coffee token allows 1 redemption
            lastRedemptionDate: lastRedemption
        };
    }

    // Get redemption stats for a user
    getUserRedemptionStats(userAddress: string, userTokens: string[] = []): UserRedemptionStats {
        const records = this.getRedemptionRecords();
        const userRedemptions = records.filter(
            record => record.userAddress.toLowerCase() === userAddress.toLowerCase()
        );

        const tokenStats = userTokens.map(tokenId => this.getTokenRedemptionStats(tokenId));
        const totalRedemptions = userRedemptions.length;
        const availableRedemptions = userTokens.length - totalRedemptions;

        return {
            userAddress: userAddress.toLowerCase(),
            totalRedemptions,
            availableRedemptions: Math.max(0, availableRedemptions),
            tokens: tokenStats
        };
    }

    // Check if a token has been redeemed
    isTokenRedeemed(tokenId: string, userAddress: string): boolean {
        const records = this.getRedemptionRecords();
        return records.some(
            record => record.tokenId === tokenId && 
                     record.userAddress.toLowerCase() === userAddress.toLowerCase()
        );
    }

    // Get all redemptions for a user
    getUserRedemptions(userAddress: string): RedemptionRecord[] {
        const records = this.getRedemptionRecords();
        return records.filter(
            record => record.userAddress.toLowerCase() === userAddress.toLowerCase()
        );
    }

    // Clear all redemption data (for testing/reset purposes)
    clearAllRedemptions(): void {
        if (typeof window === 'undefined') return;
        
        localStorage.removeItem(this.STORAGE_KEY);
        localStorage.removeItem(this.USER_STATS_KEY);
        console.log('All redemption data cleared');
    }

    // Simulate redemption for demo purposes
    async simulateRedemption(tokenId: string, userAddress: string): Promise<boolean> {
        // Add a small delay to simulate network request
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return this.recordRedemption(
            tokenId, 
            userAddress, 
            'Demo Coffee Shop',
            `0x${Math.random().toString(16).substr(2, 8)}`
        );
    }
}

// Export singleton instance
export const redemptionService = new RedemptionService();

// Export types for use in components
export type { RedemptionRecord, TokenRedemptionStats, UserRedemptionStats };
