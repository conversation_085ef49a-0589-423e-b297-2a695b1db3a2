'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFingerprint, faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { useTheme } from '@/lib/context/ThemeContext';
import { ParallaxBackground } from '@/components/ui/ParallaxBackground';
import { GlassmorphicCard } from '@/components/ui/GlassmorphicCard';
import { CrefyButton } from '@/components/ui/CrefyButton';
import { AnimatedScrollContainer } from '@/components/ui/AnimatedScrollContainer';
import { ConnectButton } from 'thirdweb/react';
import { client } from '@/config/client';
import { useProfiles } from "thirdweb/react";
import { useActiveAccount } from 'thirdweb/react';
import { authService } from '@/lib/api/useAuth';
import { signLoginPayload } from 'thirdweb/auth';
import { generatePayload, verifyPayload } from '@/lib/api/auth';
import customWallets from '@/config/connect-widget';

export default function LoginPage() {
    const { theme } = useTheme();
    const router = useRouter();
    const [authStatus, setAuthStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
    const [authError, setAuthError] = useState('');
    const account = useActiveAccount();
    const address = account?.address;

    // Get account and profiles at the top level
    const { data: profiles } = useProfiles({
        client
    });

    useEffect(() => {
        if (address) {
            if (!localStorage.getItem('walletAddress')) {
                localStorage.setItem('walletAddress', address);
            }
            setAuthStatus('loading');
            handleAuth();
        }
    }, [address]);

    const handleAuth = async () => {
        try {
            if (!profiles && !address) return;
            console.log(address);
            setAuthStatus('loading');

            const nonce = await authService.getNonce();
            console.log('Got nonce:', nonce);

            if (!nonce) {
                setAuthStatus('error');
                setAuthError('Failed to get nonce');
                return;
            }

            const message = `I am signing this message to authenticate with Crefy. Nonce: ${nonce}`;
            if (!account) {
                throw new Error('Account is undefined');
            }

            const payload = await generatePayload({
                address: account?.address,
                chainId: 17000,
            });

            const signatureResponse = await signLoginPayload({
                payload,
                account
            });
            console.log('signature', signatureResponse.signature);

            const finalResult = await verifyPayload(signatureResponse);
            console.log('finalResult', finalResult);

            // Send to server for verification
            const authResponse = await authService.login(message, signatureResponse, address as any);

            setAuthStatus('success');

            // Redirect after short delay to show success state
            setTimeout(() => {
                router.push('/dashboard');
            }, 2000);

        } catch (error) {
            console.error('Authentication error:', error);
            setAuthStatus('error');
            setAuthError(error instanceof Error ? error.message : 'Authentication failed');
        }
    };

    const handleBackToHome = () => {
        router.push('/');
    };

    return (
        <div className="min-h-screen">
            <ParallaxBackground orbCount={10}>
                {/* Additional floating elements for login page */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    {Array.from({ length: 4 }).map((_, i) => (
                        <motion.div
                            key={`login-orb-${i}`}
                            className="absolute rounded-full"
                            style={{
                                width: `${Math.random() * 120 + 60}px`,
                                height: `${Math.random() * 120 + 60}px`,
                                background: `radial-gradient(circle, ${theme.colors.primary}40, transparent)`,
                                left: `${Math.random() * 100}%`,
                                top: `${Math.random() * 100}%`,
                                filter: 'blur(30px)',
                            }}
                            animate={{
                                x: [0, Math.random() * 100 - 50, 0],
                                y: [0, Math.random() * 100 - 50, 0],
                                scale: [1, 1.2, 1],
                                opacity: [0.2, 0.5, 0.2],
                            }}
                            transition={{
                                duration: Math.random() * 10 + 15,
                                repeat: Infinity,
                                ease: "easeInOut",
                                delay: Math.random() * 3,
                            }}
                        />
                    ))}
                </div>

                <section className="min-h-screen flex items-center justify-center px-4">
                    <div className="max-w-md mx-auto w-full">
                        <AnimatedScrollContainer animation="slideUp">
                            <GlassmorphicCard padding="xl" className="text-center">
                                {/* Back Button */}
                                <div className="flex justify-start mb-6">
                                    <CrefyButton
                                        variant="glass"
                                        size="sm"
                                        onClick={handleBackToHome}
                                    >
                                        <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
                                        Back to Home
                                    </CrefyButton>
                                </div>

                                {/* Auth Icon */}
                                <motion.div
                                    className="mb-8"
                                    initial={{ scale: 0.5, opacity: 0 }}
                                    animate={{
                                        scale: authStatus === 'loading' ? [1, 1.1, 1] : 1,
                                        opacity: 1,
                                        transition: authStatus === 'loading' ? { repeat: Infinity, duration: 1.5 } : { duration: 0.6 }
                                    }}
                                >
                                    <motion.div
                                        className="w-20 h-20 mx-auto mb-6 rounded-3xl flex items-center justify-center relative"
                                        style={{
                                            backgroundColor:
                                                authStatus === 'success' ? '#4CAF50' :
                                                authStatus === 'error' ? '#F44336' :
                                                theme.colors.primary
                                        }}
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        {/* Animated ring around icon */}
                                        <motion.div
                                            className="absolute inset-0 rounded-3xl border-2"
                                            style={{ borderColor: theme.colors.primary }}
                                            animate={{
                                                scale: [1, 1.2, 1],
                                                opacity: [0.5, 0, 0.5],
                                            }}
                                            transition={{
                                                duration: 2,
                                                repeat: Infinity,
                                                ease: "easeInOut",
                                            }}
                                        />
                                        <FontAwesomeIcon
                                            icon={faFingerprint}
                                            size="2x"
                                            className="text-white"
                                        />
                                    </motion.div>
                                </motion.div>

                                {/* Title */}
                                <h1
                                    className="text-3xl font-bold mb-4"
                                    style={{ color: theme.colors.text }}
                                >
                                    {authStatus === 'idle' && 'Connect Your Wallet'}
                                    {authStatus === 'loading' && 'Authenticating...'}
                                    {authStatus === 'success' && 'Welcome to Crefy!'}
                                    {authStatus === 'error' && 'Authentication Failed'}
                                </h1>

                                {/* Description */}
                                <p
                                    className="text-lg mb-8"
                                    style={{ color: theme.colors.secondaryText }}
                                >
                                    {authStatus === 'idle' && 'Connect your wallet to access our partner companies and experience secure, decentralized identity verification.'}
                                    {authStatus === 'loading' && 'Verifying your identity using zero-knowledge proofs...'}
                                    {authStatus === 'success' && 'Authentication successful! Redirecting to your dashboard...'}
                                    {authStatus === 'error' && authError}
                                </p>

                                {/* Auth Content */}
                                <AnimatePresence mode="wait">
                                    {authStatus === 'idle' && (
                                        <motion.div
                                            key="idle"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: -20 }}
                                        >
                                            <ConnectButton
                                                client={client}
                                                wallets={customWallets}
                                                connectModal={{ size: "compact" }}
                                                connectButton={{
                                                    label: "Connect Wallet",
                                                    style: {
                                                        background: theme.colors.primary,
                                                        border: 'none',
                                                        borderRadius: '12px',
                                                        padding: '16px 32px',
                                                        color: 'white',
                                                        fontSize: '1.1rem',
                                                        fontWeight: '600',
                                                        width: '100%',
                                                        minHeight: '56px'
                                                    }
                                                }}
                                                theme='light'
                                                appMetadata={{
                                                    name: "Crefy",
                                                    url: "https://crefy.xyz",
                                                }}
                                            />
                                        </motion.div>
                                    )}

                                    {authStatus === 'loading' && (
                                        <motion.div
                                            key="loading"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: -20 }}
                                            className="space-y-4"
                                        >
                                            <div className="w-full bg-gray-200 rounded-full h-3">
                                                <div
                                                    className="h-3 rounded-full transition-all duration-1000"
                                                    style={{
                                                        backgroundColor: theme.colors.primary,
                                                        width: '70%',
                                                        animation: 'pulse 2s infinite'
                                                    }}
                                                />
                                            </div>
                                            <p
                                                className="text-sm"
                                                style={{ color: theme.colors.secondaryText }}
                                            >
                                                Please confirm the transaction in your wallet...
                                            </p>
                                        </motion.div>
                                    )}

                                    {authStatus === 'success' && (
                                        <motion.div
                                            key="success"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: -20 }}
                                            className="text-center"
                                        >
                                            <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                                            <p
                                                className="text-sm"
                                                style={{ color: theme.colors.secondaryText }}
                                            >
                                                Redirecting to dashboard...
                                            </p>
                                        </motion.div>
                                    )}

                                    {authStatus === 'error' && (
                                        <motion.div
                                            key="error"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: -20 }}
                                            className="space-y-4"
                                        >
                                            <CrefyButton
                                                variant="primary"
                                                size="lg"
                                                fullWidth
                                                onClick={() => setAuthStatus('idle')}
                                            >
                                                Try Again
                                            </CrefyButton>
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </GlassmorphicCard>
                        </AnimatedScrollContainer>
                    </div>
                </section>
            </ParallaxBackground>
        </div>
    );
}
