'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faShieldAlt,
    faLock,
    faUserCheck,
    faFingerprint,
    faKey,
    faEye,
    faArrowRight,
    faCertificate,
    faIdCard,
    faHeart
} from '@fortawesome/free-solid-svg-icons';
import { useTheme } from '@/lib/context/ThemeContext';
import { ParallaxBackground } from '@/components/ui/ParallaxBackground';
import { GlassmorphicCard } from '@/components/ui/GlassmorphicCard';
import { CrefyButton } from '@/components/ui/CrefyButton';
import { CrefyNavbar } from '@/components/ui/CrefyNavbar';
import { CrefyFooter } from '@/components/ui/CrefyFooter';
import { AnimatedScrollContainer, StaggeredContainer } from '@/components/ui/AnimatedScrollContainer';

export default function CrefyLandingPage() {
    const { theme } = useTheme();
    const router = useRouter();

    const handleGetStarted = () => {
        router.push('/login');
    };

    const handleLearnMore = () => {
        // Scroll to products section
        const productsSection = document.getElementById('products');
        if (productsSection) {
            productsSection.scrollIntoView({ behavior: 'smooth' });
        }
    };


    const products = [
        {
            icon: faIdCard,
            title: 'Crefy Passports',
            description: 'Reusable KYC solutions that streamline identity verification across platforms while maintaining user privacy and control.',
            features: ['One-time verification', 'Cross-platform compatibility', 'Privacy-preserving'],
            color: theme.colors.primary
        },
        {
            icon: faCertificate,
            title: 'Crefy Credentials',
            description: 'Digital certificates and qualifications that are verifiable, tamper-proof, and instantly shareable.',
            features: ['Instant verification', 'Tamper-proof', 'Globally recognized'],
            color: theme.colors.accent
        },
        {
            icon: faHeart,
            title: 'Crefy Memories',
            description: 'Event collectibles and digital memorabilia that capture and preserve special moments with cryptographic authenticity.',
            features: ['Authentic collectibles', 'Event participation', 'Digital memorabilia'],
            color: theme.colors.accentLight
        }
    ];

    const coreFeatures = [
        {
            icon: faShieldAlt,
            title: 'Identity Module',
            description: 'Secure, self-sovereign identity management with zero-knowledge proofs.'
        },
        {
            icon: faLock,
            title: 'Credentials Module',
            description: 'Verifiable credentials that are tamper-proof and instantly verifiable.'
        },
        {
            icon: faUserCheck,
            title: 'Attestations Module',
            description: 'Cryptographic attestations that provide trust and verification.'
        }
    ];

    return (
        <div className="min-h-screen">
            <CrefyNavbar />

            <ParallaxBackground orbCount={16}>
                {/* Additional Floating Elements */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    {Array.from({ length: 6 }).map((_, i) => (
                        <motion.div
                            key={`extra-orb-${i}`}
                            className="absolute rounded-full"
                            style={{
                                width: `${Math.random() * 150 + 80}px`,
                                height: `${Math.random() * 150 + 80}px`,
                                background: `linear-gradient(135deg, ${theme.colors.primary}30, ${theme.colors.accent}20)`,
                                left: `${Math.random() * 100}%`,
                                top: `${Math.random() * 100}%`,
                                filter: 'blur(40px)',
                            }}
                            animate={{
                                x: [0, Math.random() * 200 - 100, 0],
                                y: [0, Math.random() * 200 - 100, 0],
                                scale: [1, 1.3, 1],
                                opacity: [0.3, 0.6, 0.3],
                            }}
                            transition={{
                                duration: Math.random() * 15 + 20,
                                repeat: Infinity,
                                ease: "easeInOut",
                                delay: Math.random() * 5,
                            }}
                        />
                    ))}
                </div>

                {/* Hero Section */}
                <section className="min-h-screen flex items-center justify-center px-4 pt-16">
                    <div className="max-w-6xl mx-auto text-center">
                        <AnimatedScrollContainer animation="fadeIn" delay={0.2}>
                            <motion.div
                                className="mb-8"
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ duration: 0.8, ease: "easeOut" }}
                            >
                                <div
                                    className="w-24 h-24 mx-auto mb-6 rounded-3xl flex items-center justify-center"
                                    style={{ backgroundColor: theme.colors.primary }}
                                >
                                    <FontAwesomeIcon
                                        icon={faFingerprint}
                                        size="3x"
                                        className="text-white"
                                    />
                                </div>
                            </motion.div>
                        </AnimatedScrollContainer>

                        <AnimatedScrollContainer animation="slideUp" delay={0.4}>
                            <h1
                                className="text-6xl md:text-7xl font-bold mb-6"
                                style={{ color: theme.colors.text }}
                            >
                                Crefy
                            </h1>
                            <h2
                                className="text-2xl md:text-3xl font-semibold mb-6"
                                style={{ color: theme.colors.primary }}
                            >
                                The Future of Digital Identity
                            </h2>
                            <p
                                className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed"
                                style={{ color: theme.colors.secondaryText }}
                            >
                                Modular infrastructure for decentralized identity and credentials.
                                Secure, private, and verifiable digital identity solutions.
                            </p>
                        </AnimatedScrollContainer>

                        <AnimatedScrollContainer animation="slideUp" delay={0.6}>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                                <CrefyButton
                                    variant="primary"
                                    size="lg"
                                    onClick={handleGetStarted}
                                >
                                    Get Started
                                    <FontAwesomeIcon icon={faArrowRight} className="ml-2" />
                                </CrefyButton>
                                <CrefyButton
                                    variant="glass"
                                    size="lg"
                                    onClick={handleLearnMore}
                                >
                                    Learn More
                                </CrefyButton>
                            </div>
                        </AnimatedScrollContainer>

                        <AnimatedScrollContainer animation="fadeIn" delay={0.8}>
                            <div className="flex flex-wrap justify-center gap-8 text-center">
                                {[
                                    { icon: faShieldAlt, text: 'Secure' },
                                    { icon: faLock, text: 'Decentralized' },
                                    { icon: faUserCheck, text: 'Private' }
                                ].map((feature, index) => (
                                    <motion.div
                                        key={index}
                                        className="flex items-center space-x-3"
                                        whileHover={{ scale: 1.05 }}
                                    >
                                        <FontAwesomeIcon
                                            icon={feature.icon}
                                            size="lg"
                                            style={{ color: theme.colors.primary }}
                                        />
                                        <span
                                            className="text-lg font-medium"
                                            style={{ color: theme.colors.text }}
                                        >
                                            {feature.text}
                                        </span>
                                    </motion.div>
                                ))}
                            </div>
                        </AnimatedScrollContainer>
                    </div>
                </section>

                {/* Core Features Section */}
                <section className="py-20 px-4">
                    <div className="max-w-6xl mx-auto">
                        <AnimatedScrollContainer animation="slideUp">
                            <div className="text-center mb-16">
                                <h3
                                    className="text-4xl md:text-5xl font-bold mb-6"
                                    style={{ color: theme.colors.text }}
                                >
                                    Core Modules
                                </h3>
                                <p
                                    className="text-xl max-w-3xl mx-auto"
                                    style={{ color: theme.colors.secondaryText }}
                                >
                                    Three powerful modules working together to create a comprehensive
                                    decentralized identity infrastructure.
                                </p>
                            </div>
                        </AnimatedScrollContainer>

                        <StaggeredContainer className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {coreFeatures.map((feature, index) => (
                                <GlassmorphicCard
                                    key={index}
                                    padding="lg"
                                    hover={true}
                                    className="text-center h-full"
                                >
                                    <div
                                        className="w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center"
                                        style={{ backgroundColor: theme.colors.primary }}
                                    >
                                        <FontAwesomeIcon
                                            icon={feature.icon}
                                            size="2x"
                                            className="text-white"
                                        />
                                    </div>
                                    <h4
                                        className="text-2xl font-bold mb-4"
                                        style={{ color: theme.colors.text }}
                                    >
                                        {feature.title}
                                    </h4>
                                    <p
                                        className="text-lg leading-relaxed"
                                        style={{ color: theme.colors.secondaryText }}
                                    >
                                        {feature.description}
                                    </p>
                                </GlassmorphicCard>
                            ))}
                        </StaggeredContainer>
                    </div>
                </section>

                {/* Products Section */}
                <section id="products" className="py-20 px-4">
                    <div className="max-w-6xl mx-auto">
                        <AnimatedScrollContainer animation="slideUp">
                            <div className="text-center mb-16">
                                <h3
                                    className="text-4xl md:text-5xl font-bold mb-6"
                                    style={{ color: theme.colors.text }}
                                >
                                    Our Products
                                </h3>
                                <p
                                    className="text-xl max-w-3xl mx-auto"
                                    style={{ color: theme.colors.secondaryText }}
                                >
                                    Three innovative products built on our modular infrastructure,
                                    each designed to solve specific identity and credential challenges.
                                </p>
                            </div>
                        </AnimatedScrollContainer>

                        <StaggeredContainer className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            {products.map((product, index) => (
                                <GlassmorphicCard
                                    key={index}
                                    padding="lg"
                                    hover={true}
                                    className="h-full"
                                >
                                    <div
                                        className="w-16 h-16 mb-6 rounded-2xl flex items-center justify-center"
                                        style={{ backgroundColor: product.color }}
                                    >
                                        <FontAwesomeIcon
                                            icon={product.icon}
                                            size="2x"
                                            className="text-white"
                                        />
                                    </div>
                                    <h4
                                        className="text-2xl font-bold mb-4"
                                        style={{ color: theme.colors.text }}
                                    >
                                        {product.title}
                                    </h4>
                                    <p
                                        className="text-lg mb-6 leading-relaxed"
                                        style={{ color: theme.colors.secondaryText }}
                                    >
                                        {product.description}
                                    </p>
                                    <ul className="space-y-2 mb-6">
                                        {product.features.map((feature, featureIndex) => (
                                            <li
                                                key={featureIndex}
                                                className="flex items-center space-x-2"
                                            >
                                                <div
                                                    className="w-2 h-2 rounded-full"
                                                    style={{ backgroundColor: product.color }}
                                                />
                                                <span
                                                    className="text-sm"
                                                    style={{ color: theme.colors.text }}
                                                >
                                                    {feature}
                                                </span>
                                            </li>
                                        ))}
                                    </ul>
                                    <CrefyButton
                                        variant="glass"
                                        size="sm"
                                        fullWidth
                                        onClick={() => router.push('/login')}
                                    >
                                        Learn More
                                    </CrefyButton>
                                </GlassmorphicCard>
                            ))}
                        </StaggeredContainer>
                    </div>
                </section>

                {/* Call to Action Section */}
                <section className="py-20 px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <AnimatedScrollContainer animation="slideUp">
                            <GlassmorphicCard padding="xl">
                                <h3
                                    className="text-4xl md:text-5xl font-bold mb-6"
                                    style={{ color: theme.colors.text }}
                                >
                                    Ready to Get Started?
                                </h3>
                                <p
                                    className="text-xl mb-8 max-w-2xl mx-auto"
                                    style={{ color: theme.colors.secondaryText }}
                                >
                                    Join the future of decentralized identity. Connect with our partner
                                    companies and experience secure, private digital interactions.
                                </p>
                                <CrefyButton
                                    variant="primary"
                                    size="xl"
                                    onClick={handleGetStarted}
                                >
                                    Access Partner Companies
                                    <FontAwesomeIcon icon={faArrowRight} className="ml-2" />
                                </CrefyButton>
                            </GlassmorphicCard>
                        </AnimatedScrollContainer>
                    </div>
                </section>
            </ParallaxBackground>

            <CrefyFooter />
        </div>
    );
}