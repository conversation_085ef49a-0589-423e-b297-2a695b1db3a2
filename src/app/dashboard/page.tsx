'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
    faCoffee, 
    faArrowRight, 
    faUser, 
    faShieldAlt,
    faSignOutAlt,
    faPlus
} from '@fortawesome/free-solid-svg-icons';
import { useTheme } from '@/lib/context/ThemeContext';
import { ParallaxBackground } from '@/components/ui/ParallaxBackground';
import { GlassmorphicCard } from '@/components/ui/GlassmorphicCard';
import { CrefyButton } from '@/components/ui/CrefyButton';
import { CrefyNavbar } from '@/components/ui/CrefyNavbar';
import { CrefyFooter } from '@/components/ui/CrefyFooter';
import { AnimatedScrollContainer, StaggeredContainer } from '@/components/ui/AnimatedScrollContainer';
import { authService } from '@/lib/api/useAuth';

interface PartnerCompany {
    id: string;
    name: string;
    description: string;
    icon: any;
    route: string;
    color: string;
    category: string;
    status: 'active' | 'coming-soon';
}

export default function DashboardPage() {
    const { theme } = useTheme();
    const router = useRouter();
    const [userAddress, setUserAddress] = useState<string>('');

    useEffect(() => {
        // Check if user is authenticated
        const authToken = localStorage.getItem('authToken');
        const walletAddress = localStorage.getItem('walletAddress');
        
        if (!authToken) {
            router.push('/login');
            return;
        }

        if (walletAddress) {
            setUserAddress(walletAddress);
        }
    }, [router]);

    const handleLogout = () => {
        authService.logout();
        router.push('/');
    };

    const partnerCompanies: PartnerCompany[] = [
        {
            id: 'project-mocha',
            name: 'Project Mocha',
            description: 'Premium coffee experiences with NFT tokens.',
            icon: faCoffee,
            route: '/project-mocha',
            color: '#D97706', // Coffee brown/amber
            category: 'Food & Beverage',
            status: 'active'
        },
        // Add more partner companies here as they become available
        {
            id: 'coming-soon-1',
            name: 'More Partners',
            description: 'Additional partners coming soon.',
            icon: faPlus,
            route: '#',
            color: theme.colors.secondary,
            category: 'Coming Soon',
            status: 'coming-soon'
        }
    ];

    const handleCompanySelect = (company: PartnerCompany) => {
        if (company.status === 'active') {
            router.push(company.route);
        }
    };



    return (
        <div className="min-h-screen">
            <CrefyNavbar
                showAuth={true}
                onLogout={handleLogout}
                userAddress={userAddress}
            />
            
            <ParallaxBackground>
                {/* Header Section */}
                <section className="pt-10 pb-5 px-2">
                </section>

                {/* Partner Companies Section */}
                <section className="py-16 px-4">
                    <div className="max-w-6xl mx-auto">
                        <AnimatedScrollContainer animation="slideUp">
                            <div className="text-center mb-12">
                                <h2
                                    className="text-2xl md:text-3xl font-bold mb-4"
                                    style={{ color: theme.colors.text }}
                                >
                                    Partners
                                </h2>
                            </div>
                        </AnimatedScrollContainer>

                        <StaggeredContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {partnerCompanies.map((company) => (
                                <GlassmorphicCard
                                    key={company.id}
                                    padding="lg"
                                    hover={company.status === 'active'}
                                    onClick={() => handleCompanySelect(company)}
                                    className={`h-full ${company.status === 'active' ? 'cursor-pointer' : 'cursor-not-allowed opacity-75'}`}
                                >
                                    <div className="flex flex-col h-full">
                                        {/* Company Icon */}
                                        <div
                                            className="w-16 h-16 mb-6 rounded-2xl flex items-center justify-center"
                                            style={{ backgroundColor: company.color }}
                                        >
                                            <FontAwesomeIcon
                                                icon={company.icon}
                                                size="2x"
                                                className="text-white"
                                            />
                                        </div>

                                        {/* Company Info */}
                                        <div className="flex-1">
                                            <div className="flex items-center justify-between mb-2">
                                                <h3
                                                    className="text-xl font-bold"
                                                    style={{ color: theme.colors.text }}
                                                >
                                                    {company.name}
                                                </h3>
                                                <span
                                                    className={`text-xs px-2 py-1 rounded-full ${
                                                        company.status === 'active' 
                                                            ? 'bg-green-100 text-green-800' 
                                                            : 'bg-gray-100 text-gray-600'
                                                    }`}
                                                >
                                                    {company.status === 'active' ? 'Active' : 'Coming Soon'}
                                                </span>
                                            </div>
                                            
                                            <p
                                                className="text-sm mb-4 text-"
                                                style={{ color: company.category }}
                                            >
                                                {company.category}
                                            </p>
                                            
                                            <p
                                                className="text-sm mb-6 leading-relaxed"
                                                style={{ color: theme.colors.secondaryText }}
                                            >
                                                {company.description}
                                            </p>
                                        </div>

                                        {/* Action Button */}
                                        <CrefyButton
                                            variant={company.status === 'active' ? 'primary' : 'glass'}
                                            size="sm"
                                            fullWidth
                                            disabled={company.status !== 'active'}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleCompanySelect(company);
                                            }}
                                        >
                                            {company.status === 'active' ? (
                                                <>
                                                    Access Platform
                                                    <FontAwesomeIcon icon={faArrowRight} className="ml-2" />
                                                </>
                                            ) : (
                                                'Coming Soon'
                                            )}
                                        </CrefyButton>
                                    </div>
                                </GlassmorphicCard>
                            ))}
                        </StaggeredContainer>
                    </div>
                </section>

                {/* Security Notice */}
                <section className="py-16 px-4">
                    <div className="max-w-4xl mx-auto">
                        <AnimatedScrollContainer animation="slideUp">
                            <GlassmorphicCard padding="lg" className="text-center">
                                <div
                                    className="w-12 h-12 mx-auto mb-4 rounded-xl flex items-center justify-center"
                                    style={{ backgroundColor: theme.colors.primary }}
                                >
                                    <FontAwesomeIcon
                                        icon={faShieldAlt}
                                        size="lg"
                                        className="text-white"
                                    />
                                </div>
                                <h3
                                    className="text-xl font-bold mb-3"
                                    style={{ color: theme.colors.text }}
                                >
                                    Privacy Protected
                                </h3>
                                <p
                                    className="text-sm max-w-xl mx-auto"
                                    style={{ color: theme.colors.secondaryText }}
                                >
                                    Zero-knowledge proofs keep your data private and secure.
                                </p>
                            </GlassmorphicCard>
                        </AnimatedScrollContainer>
                    </div>
                </section>
            </ParallaxBackground>

            <CrefyFooter />
        </div>
    );
}
